import { PrismaClient } from '../generated/prisma';

const prisma = new PrismaClient();

// Insert products
async function main() {
    await prisma.product.createMany({
        data: [
            {
                name: 'AC Events Enterprise',
                vectorStorageId: 'vs_68540fd8b1d08191aa96a4f5aeebb314',
                lastestVersion: '1.0.0',
                docs: {
                    create: [
                        {
                            name: 'Guide',
                            version: '1.0.0',
                            vectorFileId: 'file_68540fd8b1d08191aa96a4f5aeebb314',
                            googleDocId: '1jy5z6z6z6z6z6z6z6z6z6z6z6z6z6z6z6',
                        },
                        {
                            name: 'AC Events Enterprise 1.0.1',
                            version: '1.0.1',
                            vectorFileId: 'file_68540fd8b1d08191aa96a4f5aeebb314',
                    ],
                }
            },
            { name: 'AC MemberSmart', vectorStorageId: 'vs_68541008b48c81919539ea21618136be', lastestVersion: '1.0.0' },
        ],
    });

}

main()
    .then(async () => {
        await prisma.$disconnect()
    })
    .catch(async (e) => {
        console.error(e)
        await prisma.$disconnect()
        process.exit(1)
    })