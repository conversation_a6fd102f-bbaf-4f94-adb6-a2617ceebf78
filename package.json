{"name": "prisma-example", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node dist/index.js", "dev": "nodemon"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.1", "nodemon": "^3.1.10", "prisma": "^6.10.1", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.9.0", "@slack/web-api": "^7.9.2", "dotenv": "^16.5.0", "express": "^5.1.0", "openai": "^5.3.0", "zod": "^3.25.67"}}